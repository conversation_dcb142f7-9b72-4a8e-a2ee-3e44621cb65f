from enum import Enum

from src.ai.tools import TOOLS_HIDELIST
from src.messaging.tools import TOOL_EMOJIS, TOOL_NAMES_CALLED, TOOL_NAMES_ERROR, TOOL_NAMES_SUCCESS


class MessageComponentType(Enum):
    TEXT = "text"
    TOOL = "tool"

    BASE = "base"


class MessageComponent:
    """Base class for all message components"""

    component_type: MessageComponentType

    def __init__(self, content: str, component_type: MessageComponentType):
        self.content = content
        self.component_type = component_type

    def get_content(self) -> str:
        return self.content

    def update_content(self, new_content: str) -> None:
        self.content = new_content


class TextComponent(MessageComponent):
    """Component for regular text content"""

    def __init__(self, text: str):
        super().__init__(text, MessageComponentType.TEXT)


class ToolComponent(MessageComponent):
    """Component for tool calls with status tracking"""

    def __init__(
            self,
            tool_name: str,
            tool_call_id: str,
            finished: bool = False,
            is_error: bool = False
    ):
        self.tool_name = tool_name
        self.tool_call_id = tool_call_id
        self.finished = finished
        self.is_error = is_error

        content = self._format_content()
        super().__init__(content, MessageComponentType.TOOL)

    def _format_content(self) -> str:
        """Format the tool content based on status"""

        if self.tool_name in TOOLS_HIDELIST:
            return ""

        if not self.finished:
            return f"{TOOL_EMOJIS.get(self.tool_name, '🔧 Tool')} <code>{TOOL_NAMES_CALLED.get(self.tool_name, self.tool_name)}</code>"
        else:
            if self.is_error:
                status_icon = "❌"
                status_text = "failed"
            else:
                status_icon = TOOL_EMOJIS.get(self.tool_name, "✅")
                status_text = ""

            return f"{TOOL_EMOJIS.get(self.tool_name, status_icon + ' Tool')} <code>{TOOL_NAMES_ERROR.get(self.tool_name, self.tool_name ) if self.is_error else TOOL_NAMES_SUCCESS.get(self.tool_name, self.tool_name)}</code>"

    def update_status(self, finished: bool = False, is_error: bool = False) -> None:
        self.finished = finished
        self.is_error = is_error
        self.content = self._format_content()
